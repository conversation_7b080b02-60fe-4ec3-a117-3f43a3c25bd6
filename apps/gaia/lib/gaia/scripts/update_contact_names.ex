defmodule Gaia.Scripts.UpdateContactNames do
  @moduledoc """
  <PERSON>ript to update contacts without first_name and last_name using their shareholding account names.
  
  This script:
  1. Finds all contacts without first_name and last_name who have shareholdings
  2. Updates those contacts with the account_name from their first shareholding
  3. Processes updates in chunks to handle large datasets efficiently
  
  Usage in iex:
    # Update all contacts across all companies
    Gaia.Scripts.UpdateContactNames.run()
    
    # Update contacts for a specific company
    Gaia.Scripts.UpdateContactNames.run(company_profile_id: 123)
    
    # Use custom chunk size (default is 100)
    Gaia.Scripts.UpdateContactNames.run(chunk_size: 50)
    
    # Dry run to see what would be updated without making changes
    Gaia.Scripts.UpdateContactNames.run(dry_run: true)
  """
  
  import Ecto.Query, warn: false
  
  alias Gaia.Contacts.Contact
  alias Gaia.Registers.Shareholding
  alias Gaia.Repo
  
  require Logger
  
  @default_chunk_size 100
  
  @doc """
  Main function to run the contact name update script.
  
  Options:
  - `:company_profile_id` - Only update contacts for this company (optional)
  - `:chunk_size` - Number of contacts to process per chunk (default: 100)
  - `:dry_run` - If true, only shows what would be updated without making changes (default: false)
  """
  def run(opts \\ []) do
    company_profile_id = Keyword.get(opts, :company_profile_id)
    chunk_size = Keyword.get(opts, :chunk_size, @default_chunk_size)
    dry_run = Keyword.get(opts, :dry_run, false)
    
    Logger.info("Starting contact names update script...")
    Logger.info("Options: #{inspect(opts)}")
    
    # Find contacts that need updating
    contacts_to_update = find_contacts_needing_update(company_profile_id)
    total_count = length(contacts_to_update)
    
    Logger.info("Found #{total_count} contacts that need name updates")
    
    if total_count == 0 do
      Logger.info("No contacts need updating. Script completed.")
      {:ok, %{updated: 0, errors: 0}}
    else
      # Process in chunks
      process_contacts_in_chunks(contacts_to_update, chunk_size, dry_run)
    end
  end
  
  @doc """
  Finds contacts that need name updates.
  Returns a list of contact IDs with their first shareholding account name.
  """
  def find_contacts_needing_update(company_profile_id \\ nil) do
    base_query = 
      from(c in Contact,
        join: sh in Shareholding,
        on: sh.contact_id == c.id,
        where: (is_nil(c.first_name) or c.first_name == "") and 
               (is_nil(c.last_name) or c.last_name == "") and
               not is_nil(sh.account_name) and
               sh.account_name != "" and
               not c.invalidated,
        group_by: c.id,
        select: %{
          contact_id: c.id,
          account_name: min(sh.account_name)  # Use first alphabetically as a consistent choice
        }
      )
    
    query = case company_profile_id do
      nil -> base_query
      id -> where(base_query, [c], c.company_profile_id == ^id)
    end
    
    Repo.all(query)
  end
  
  @doc """
  Processes contacts in chunks to avoid overwhelming the database.
  """
  def process_contacts_in_chunks(contacts_data, chunk_size, dry_run) do
    total_count = length(contacts_data)
    chunks = Enum.chunk_every(contacts_data, chunk_size)
    total_chunks = length(chunks)
    
    Logger.info("Processing #{total_count} contacts in #{total_chunks} chunks of size #{chunk_size}")
    
    {updated_count, error_count} = 
      chunks
      |> Enum.with_index(1)
      |> Enum.reduce({0, 0}, fn {chunk, chunk_index}, {acc_updated, acc_errors} ->
        Logger.info("Processing chunk #{chunk_index}/#{total_chunks} (#{length(chunk)} contacts)")
        
        {chunk_updated, chunk_errors} = process_chunk(chunk, dry_run)
        
        Logger.info("Chunk #{chunk_index} completed: #{chunk_updated} updated, #{chunk_errors} errors")
        
        {acc_updated + chunk_updated, acc_errors + chunk_errors}
      end)
    
    Logger.info("Script completed: #{updated_count} contacts updated, #{error_count} errors")
    
    {:ok, %{updated: updated_count, errors: error_count}}
  end
  
  @doc """
  Processes a single chunk of contacts.
  """
  def process_chunk(contacts_data, dry_run) do
    contacts_data
    |> Enum.reduce({0, 0}, fn %{contact_id: contact_id, account_name: account_name}, {updated, errors} ->
      case update_contact_name(contact_id, account_name, dry_run) do
        {:ok, _} -> {updated + 1, errors}
        {:error, reason} -> 
          Logger.error("Failed to update contact #{contact_id}: #{inspect(reason)}")
          {updated, errors + 1}
      end
    end)
  end
  
  @doc """
  Updates a single contact's first_name with the account name.
  """
  def update_contact_name(contact_id, account_name, dry_run \\ false) do
    # Extract and clean the name
    cleaned_name = extract_contact_first_name(account_name)
    
    if dry_run do
      Logger.info("DRY RUN: Would update contact #{contact_id} with first_name: '#{cleaned_name}'")
      {:ok, :dry_run}
    else
      case Repo.get(Contact, contact_id) do
        nil ->
          {:error, :contact_not_found}
          
        contact ->
          changeset = Contact.changeset(contact, %{first_name: cleaned_name})
          
          case Repo.update(changeset) do
            {:ok, updated_contact} ->
              Logger.debug("Updated contact #{contact_id} with first_name: '#{cleaned_name}'")
              {:ok, updated_contact}
              
            {:error, changeset} ->
              {:error, changeset}
          end
      end
    end
  end
  
  @doc """
  Extracts and cleans the contact first name from account name.
  This function is based on the existing logic in the codebase.
  """
  def extract_contact_first_name(account_name) when is_binary(account_name) do
    account_name
    |> String.trim()
    |> case do
      "" -> nil
      cleaned_name -> cleaned_name
    end
  end
  
  def extract_contact_first_name(_), do: nil
end
