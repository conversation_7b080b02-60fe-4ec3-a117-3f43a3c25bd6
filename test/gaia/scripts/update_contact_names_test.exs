defmodule Gaia.Scripts.UpdateContactNamesTest do
  use Gaia.DataCase, async: false

  alias Gaia.Contacts.Contact
  alias Gaia.Registers.Shareholding
  alias Gaia.Scripts.UpdateContactNames

  describe "UpdateContactNames script" do
    setup :company_profile_builder

    test "finds contacts needing update", %{company_profile: company_profile} do
      company_profile_id = company_profile.id

      # Create a contact without names but with shareholding
      {:ok, contact_without_names} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      # Create a contact with names (should not be found)
      {:ok, contact_with_names} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          first_name: "<PERSON>",
          last_name: "<PERSON><PERSON>",
          email: "<EMAIL>"
        })
        |> Repo.insert()

      # Create a contact without names and no shareholding (should not be found)
      {:ok, _contact_no_shareholding} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      # Create shareholdings
      {:ok, _shareholding1} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact_without_names.id,
          account_name: "Test Account Name",
          registry_holder_id: "REG001"
        })
        |> Repo.insert()

      {:ok, _shareholding2} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact_with_names.id,
          account_name: "Another Account",
          registry_holder_id: "REG002"
        })
        |> Repo.insert()

      # Test finding contacts needing update
      contacts_to_update = UpdateContactNames.find_contacts_needing_update(company_profile_id)

      assert length(contacts_to_update) == 1
      assert hd(contacts_to_update).contact_id == contact_without_names.id
      assert hd(contacts_to_update).account_name == "Test Account Name"
    end

    test "updates contact name correctly", %{company_profile: company_profile} do
      company_profile_id = company_profile.id

      # Create a contact without names
      {:ok, contact} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      # Update the contact name
      {:ok, updated_contact} = UpdateContactNames.update_contact_name(contact.id, "New Name")

      assert updated_contact.first_name == "New Name"
      assert updated_contact.last_name == nil
    end

    test "dry run does not update database", %{company_profile: company_profile} do
      company_profile_id = company_profile.id

      # Create a contact without names
      {:ok, contact} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      # Dry run update
      {:ok, :dry_run} = UpdateContactNames.update_contact_name(contact.id, "New Name", true)

      # Verify contact was not updated
      unchanged_contact = Repo.get(Contact, contact.id)
      assert unchanged_contact.first_name == nil
    end

    test "extract_contact_first_name function", %{} do
      assert UpdateContactNames.extract_contact_first_name("John Doe") == "John Doe"
      assert UpdateContactNames.extract_contact_first_name("  Jane Smith  ") == "Jane Smith"
      assert UpdateContactNames.extract_contact_first_name("ACME Corporation") == "ACME Corporation"
      assert UpdateContactNames.extract_contact_first_name("") == nil
      assert UpdateContactNames.extract_contact_first_name("   ") == nil
      assert UpdateContactNames.extract_contact_first_name(nil) == nil
      assert UpdateContactNames.extract_contact_first_name(123) == nil
    end

    test "full script run with dry_run", %{company_profile: company_profile} do
      company_profile_id = company_profile.id

      # Create contacts and shareholdings
      {:ok, contact1} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      {:ok, contact2} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      {:ok, _shareholding1} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact1.id,
          account_name: "Account One",
          registry_holder_id: "REG001"
        })
        |> Repo.insert()

      {:ok, _shareholding2} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact2.id,
          account_name: "Account Two",
          registry_holder_id: "REG002"
        })
        |> Repo.insert()

      # Run script with dry_run
      {:ok, result} = UpdateContactNames.run(
        company_profile_id: company_profile_id,
        dry_run: true,
        chunk_size: 1
      )

      assert result.updated == 2
      assert result.errors == 0

      # Verify contacts were not actually updated
      unchanged_contact1 = Repo.get(Contact, contact1.id)
      unchanged_contact2 = Repo.get(Contact, contact2.id)
      assert unchanged_contact1.first_name == nil
      assert unchanged_contact2.first_name == nil
    end

    test "full script run with actual updates", %{company_profile: company_profile} do
      company_profile_id = company_profile.id

      # Create contacts and shareholdings
      {:ok, contact1} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      {:ok, contact2} = 
        %Contact{}
        |> Contact.changeset(%{
          company_profile_id: company_profile_id,
          email: "<EMAIL>"
        })
        |> Repo.insert()

      {:ok, _shareholding1} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact1.id,
          account_name: "Account One",
          registry_holder_id: "REG001"
        })
        |> Repo.insert()

      {:ok, _shareholding2} = 
        %Shareholding{}
        |> Shareholding.changeset(%{
          company_profile_id: company_profile_id,
          contact_id: contact2.id,
          account_name: "Account Two",
          registry_holder_id: "REG002"
        })
        |> Repo.insert()

      # Run script with actual updates
      {:ok, result} = UpdateContactNames.run(
        company_profile_id: company_profile_id,
        chunk_size: 1
      )

      assert result.updated == 2
      assert result.errors == 0

      # Verify contacts were actually updated
      updated_contact1 = Repo.get(Contact, contact1.id)
      updated_contact2 = Repo.get(Contact, contact2.id)
      assert updated_contact1.first_name == "Account One"
      assert updated_contact2.first_name == "Account Two"
    end
  end
end
